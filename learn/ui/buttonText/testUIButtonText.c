#define SDL_MAIN_USE_CALLBACKS 1
#include <SDL3/SDL.h>
#include <SDL3/SDL_main.h>
#include <SDL3_ttf/SDL_ttf.h>
#include "ButtonText.h"

// Application context
typedef struct
{
	// Window and device
	SDL_Window *window;
	SDL_GPUDevice *device;
	int window_width;
	int window_height;
	float last_tick;
	bool app_in_background;
	bool running;
	bool window_resized;

	// GPU resources
	SDL_GPUCommandBuffer *cmd_buf;

	// Button
	Button button;

	// Text rendering using UIText library
	UITextContext *text_context;
	TTF_Font *font;
	bool use_SDF;
	const char *font_filename;
	bool enable_kerning;
	int font_size;
	float letter_spacing;  // Custom letter spacing multiplier (1.0 = normal, 0.8 = tighter, 1.2 = looser)
} Context;

// Error checking helper
void *check_error_ptr(void *ptr)
{
	if (!ptr)
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "%s", SDL_GetError());
	}
	return ptr;
}

// Initialize the application
int init(Context *context)
{
	// Initialize SDL
	if (!SDL_Init(SDL_INIT_VIDEO | SDL_INIT_EVENTS))
	{
		SDL_Log("SDL initialization failed: %s", SDL_GetError());
		return -1;
	}

	// Create window
	context->window_width = 800;
	context->window_height = 600;
	context->window = SDL_CreateWindow("UI Button Test", context->window_width, context->window_height, SDL_WINDOW_RESIZABLE);
	if (!context->window)
	{
		SDL_Log("Window creation failed: %s", SDL_GetError());
		return -1;
	}

	// Create GPU device
	context->device = SDL_CreateGPUDevice(
			SDL_GPU_SHADERFORMAT_SPIRV | SDL_GPU_SHADERFORMAT_DXIL | SDL_GPU_SHADERFORMAT_MSL,
			false, NULL);
	if (!context->device)
	{
		SDL_Log("GPU device creation failed: %s", SDL_GetError());
		return -1;
	}

	// Claim window for GPU device
	if (!SDL_ClaimWindowForGPUDevice(context->device, context->window))
	{
		SDL_Log("Failed to claim window for GPU device: %s", SDL_GetError());
		return -1;
	}

	// Initialize button rendering resources
	if (!Button_InitRenderResources(context->device, context->window)) {
		SDL_Log("Failed to initialize button rendering resources!");
		return -1;
	}

	// Initialize TTF
	if (!TTF_Init())
	{
		SDL_Log("TTF initialization failed: %s", SDL_GetError());
		return -1;
	}

	// Load font with configurable size
	context->font = TTF_OpenFont(context->font_filename, context->font_size);
	if (!context->font)
	{
		SDL_Log("Failed to load font: %s", SDL_GetError());
		return -1;
	}

	// Adjust letter spacing by controlling kerning
	TTF_SetFontKerning(context->font, context->enable_kerning ? 1 : 0);

	SDL_Log("Font size: %d, Kerning: %s", context->font_size, context->enable_kerning ? "enabled" : "disabled");

	SDL_Log("SDF %s", context->use_SDF ? "enabled" : "disabled");

	// Create UIText context
	context->text_context = UIText_CreateContext(context->device, context->window, context->window_width, context->window_height, context->use_SDF);
	if (!context->text_context)
	{
		SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create UIText context");
		return -1;
	}

	// Initialize button with integrated text creation
	Button_Init(&context->button, 100.0f, 100.0f, 150.0f, 50.0f, "CHECKED", context->text_context, context->font);

	context->running = true;
	context->app_in_background = false;
	context->window_resized = false;

	return 0;
}

// Handle window resize
int handle_window_resize(Context *context)
{
	if (!context->window_resized)
	{
		return 0;
	}

	SDL_Log("Window resized to %dx%d", context->window_width, context->window_height);

	// Update button position to maintain position from top left
	float button_x = 100.0f;
	float button_y = 100.0f;
	Button_UpdatePosition(&context->button, button_x, button_y);
	// Text position is automatically updated in Button_UpdatePosition

	context->window_resized = false;
	return 0;
}

// Update function
int update(Context *context)
{
	// Handle window resize if needed
	if (handle_window_resize(context) < 0)
	{
		return -1;
	}

	return 0;
}


// Render function combining button and text rendering
int render(Context *context)
{
	// Acquire command buffer
	context->cmd_buf = SDL_AcquireGPUCommandBuffer(context->device);
	if (!context->cmd_buf)
	{
		SDL_Log("Failed to acquire command buffer: %s", SDL_GetError());
		return -1;
	}

	// Prepare text data first (must be done outside render pass) - now integrated into Button_Update
	Button_Update(&context->button, 1, context->text_context, context->cmd_buf);

	// Acquire swapchain texture
	SDL_GPUTexture *swapchain_texture;
	if (!SDL_WaitAndAcquireGPUSwapchainTexture(context->cmd_buf, context->window, &swapchain_texture, NULL, NULL))
	{
		SDL_Log("Failed to acquire swapchain texture: %s", SDL_GetError());
		return -1;
	}

	if (swapchain_texture != NULL)
	{
		// Upload shared vertex data on first frame (could be done once during init)
		static bool shared_data_uploaded = false;
		if (!shared_data_uploaded) {
			Button_UploadSharedData(context->cmd_buf);
			shared_data_uploaded = true;
		}

		// Begin render pass for both button and text
		SDL_GPUColorTargetInfo color_target_info = {0};
		color_target_info.texture = swapchain_texture;
		color_target_info.clear_color = (SDL_FColor){0.2f, 0.2f, 0.2f, 1.0f}; // Dark gray background
		color_target_info.load_op = SDL_GPU_LOADOP_CLEAR;
		color_target_info.store_op = SDL_GPU_STOREOP_STORE;

		SDL_GPURenderPass *render_pass = SDL_BeginGPURenderPass(context->cmd_buf, &color_target_info, 1, NULL);

		// Draw button and text together - now integrated into Button_Draw
		Button_Draw(render_pass, context->cmd_buf, NULL, &context->button, 1, context->window_width, context->window_height, context->text_context);

		SDL_EndGPURenderPass(render_pass);
	}

	// Submit command buffer
	SDL_SubmitGPUCommandBuffer(context->cmd_buf);

	return 0;
}

// SDL app callbacks
SDL_AppResult SDL_AppInit(void **appstate, int argc, char *argv[])
{
	Context *context = SDL_calloc(1, sizeof(Context));
	if (!context)
	{
		SDL_Log("Failed to allocate context");
		return SDL_APP_FAILURE;
	}
	*appstate = context;

	// Set default font and SDF settings
	context->font_filename = "assets/fonts/sudo/Sudo-Regular.ttf";
	context->use_SDF = true;
	context->enable_kerning = true;  // Default: enable kerning (tighter spacing)
	context->font_size = 24;         // Default font size
	context->letter_spacing = 1.0f;  // Default: normal letter spacing

	// Parse command line arguments
	for (int i = 1; i < argc; ++i)
	{
		if (SDL_strcasecmp(argv[i], "--no-sdf") == 0)
		{
			context->use_SDF = false;
		}
		else if (SDL_strcasecmp(argv[i], "--no-kerning") == 0)
		{
			context->enable_kerning = false;  // Disable kerning for looser spacing
		}
		else if (SDL_strcasecmp(argv[i], "--font-size") == 0 && i + 1 < argc)
		{
			context->font_size = SDL_atoi(argv[++i]);  // Custom font size
		}
	}

	if (init(context) < 0)
	{
		return SDL_APP_FAILURE;
	}

	return SDL_APP_CONTINUE;
}

SDL_AppResult SDL_AppEvent(void *appstate, SDL_Event *event)
{
	Context *context = (Context *)appstate;

	switch (event->type)
	{
	case SDL_EVENT_KEY_UP:
		if (event->key.key == SDLK_ESCAPE)
		{
			context->running = false;
		}
		break;

	case SDL_EVENT_QUIT:
		context->running = false;
		break;

	case SDL_EVENT_WILL_ENTER_BACKGROUND:
		context->app_in_background = true;
		break;

	case SDL_EVENT_DID_ENTER_FOREGROUND:
		context->app_in_background = false;
		break;

	case SDL_EVENT_WINDOW_RESIZED:
		context->window_width = event->window.data1;
		context->window_height = event->window.data2;
		context->window_resized = true;
		break;

	case SDL_EVENT_MOUSE_MOTION:
	{
		// Check if mouse is over button
		float mouse_x = event->motion.x;
		float mouse_y = event->motion.y;
		Button_HandleMouseMotion(&context->button, mouse_x, mouse_y);
		break;
	}

	case SDL_EVENT_MOUSE_BUTTON_DOWN:
	{
		// Check if button is clicked
		if (event->button.button == SDL_BUTTON_LEFT)
		{
			float mouse_x = event->button.x;
			float mouse_y = event->button.y;
			Button_HandleMouseButtonDown(&context->button, mouse_x, mouse_y, event->button.button);
		}
		break;
	}

	case SDL_EVENT_MOUSE_BUTTON_UP:
	{
		// Check if button is released
		if (event->button.button == SDL_BUTTON_LEFT)
		{
			float mouse_x = event->button.x;
			float mouse_y = event->button.y;
			if (Button_HandleMouseButtonUp(&context->button, mouse_x, mouse_y, event->button.button))
			{
				// Button clicked!
				SDL_Log("helloworld");
			}
		}
		break;
	}
	}

	return context->running ? SDL_APP_CONTINUE : SDL_APP_SUCCESS;
}

SDL_AppResult SDL_AppIterate(void *appstate)
{
	Context *context = (Context *)appstate;

	if (context->app_in_background)
	{
		SDL_Delay(100);
		return SDL_APP_CONTINUE;
	}

	// Update timing
	context->last_tick = SDL_GetTicks() / 1000.0f;

	// Update
	if (update(context) < 0)
	{
		return SDL_APP_FAILURE;
	}

	// Render
	if (render(context) < 0)
	{
		return SDL_APP_FAILURE;
	}

	return SDL_APP_CONTINUE;
}

void cleanup(Context *context)
{
	// Cleanup button text objects - now integrated into button
	Button_DestroyText(&context->button);

	// Cleanup UIText context
	if (context->text_context)
	{
		UIText_DestroyContext(context->text_context);
	}

	// Cleanup font and TTF
	if (context->font)
	{
		TTF_CloseFont(context->font);
	}
	TTF_Quit();

	// Clean up button rendering resources
	Button_CleanupRenderResources();

	// Clean up window and device
	SDL_ReleaseWindowFromGPUDevice(context->device, context->window);
	SDL_DestroyGPUDevice(context->device);
	SDL_DestroyWindow(context->window);
	SDL_Quit();
}

void SDL_AppQuit(void *appstate, SDL_AppResult result)
{
	(void)result; // Unused parameter
	Context *context = (Context *)appstate;
	if (context)
	{
		cleanup(context);
		SDL_free(context);
	}
}