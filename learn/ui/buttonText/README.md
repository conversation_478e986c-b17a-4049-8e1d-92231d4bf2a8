# Button with Text Rendering

This folder contains an implementation of UI buttons with integrated text rendering using SDL3 and SDL3_ttf. The implementation combines uniform-based button rendering with proper text centering and positioning.

## Key Features

- **Uniform-based button rendering** - Uses transform matrices passed as uniforms to shaders instead of per-button vertex data
- **Integrated text rendering** - Text creation, positioning, and rendering is handled within button functions
- **Proper text centering** - Uses font metrics for accurate horizontal and vertical text alignment
- **Efficient rendering** - Shared vertex data across all buttons with individual transform matrices
- **Mouse interaction** - Hover and click state handling with visual feedback

## Files

- `ButtonText.h` - Header file with button structure and function declarations
- `ButtonText.c` - Implementation of button rendering and text integration
- `testUIButtonText.c` - Test application demonstrating button with text functionality

## Text Centering Implementation

### Problem Solved
Instead of using hardcoded offsets (`-30.0f`, `-12.0f`) for text positioning, the implementation now uses proper font metrics for accurate centering.

### Font Metrics Used
- `TTF_GetFontAscent()` - Gets the font's ascent (positive value from baseline to top)
- `TTF_GetFontDescent()` - Gets the font's descent (negative value from baseline to bottom)  
- `TTF_GetTextSize()` - Gets the actual text dimensions (width and height)

### Visual Center Calculation
```c
// Center vertically using font metrics for better alignment
int font_ascent = TTF_GetFontAscent(font);
int font_descent = TTF_GetFontDescent(font);
int font_height = font_ascent - font_descent; // Total font height

// Position text so that the visual center aligns with button center
float visual_center_offset = (font_ascent - font_height / 2.0f);
float text_y = y + height / 2.0f - visual_center_offset;
```

### How It Works
1. **Font ascent** is the distance from baseline to the top of tallest characters
2. **Font descent** is the distance from baseline to the bottom of lowest characters (negative value)
3. **Visual center** is calculated as the midpoint between ascent and descent
4. **Text baseline** is positioned so the visual center aligns with button center

## Architecture

### Button Structure
```c
typedef struct Button {
    SDL_FRect rect;           // Button position and size
    SDL_FColor color;         // Normal button color
    SDL_FColor hoverColor;    // Hover state color
    SDL_FColor textColor;     // Text color
    char* text;              // Button text string
    UITextObject* text_object; // Integrated text object
    bool isHovered;          // Hover state
    bool isPressed;          // Press state
    bool isDirty;            // Needs redraw flag
} Button;
```

### Integration with UIText Library
- `Button_Init()` creates both button and text object with proper centering
- `Button_Update()` prepares text data for rendering (called outside render pass)
- `Button_Draw()` renders both button and text within the same render pass
- `Button_UpdatePosition()` maintains text centering when button moves

## Rendering Pipeline

1. **Initialization**
   - `Button_InitRenderResources()` - Sets up GPU resources and shaders
   - `Button_Init()` - Creates button with integrated text object

2. **Per-Frame Updates**
   - `Button_Update()` - Prepares text geometry data (outside render pass)
   - `Button_UploadSharedData()` - Uploads shared quad vertex data (once)

3. **Rendering**
   - `Button_Draw()` - Renders buttons using transform matrices
   - Text is rendered on top within the same render pass

## Coordinate System

- **Button coordinates** - Top-left origin (0,0 at top-left of window)
- **Text positioning** - Automatically converted to match button coordinate system
- **NDC conversion** - Transform matrices convert to Normalized Device Coordinates for GPU

## Usage Example

```c
// Initialize button rendering resources
Button_InitRenderResources(device, window);

// Create button with text
Button button;
Button_Init(&button, 100.0f, 100.0f, 150.0f, 50.0f, 
           "Click Me!", text_context, font);

// In render loop
Button_Update(&button, 1, text_context, cmd_buf);  // Outside render pass
Button_Draw(render_pass, cmd_buf, NULL, &button, 1, 
           window_width, window_height, text_context);  // Inside render pass
```

## Dependencies

- SDL3 - Core graphics and windowing
- SDL3_ttf - TrueType font rendering
- UIText library - Text rendering integration (`learn/TTF/uiText/`)

## Shader Requirements

- Vertex shader with transform matrix uniform
- Fragment shader with color uniform
- Compatible with SPIRV, DXIL, and MSL shader formats

## Performance Considerations

- **Shared vertex data** - Single quad geometry shared across all buttons
- **Transform matrices** - Individual positioning via uniforms instead of vertex data
- **Batch text rendering** - All button text rendered together for efficiency
- **Dirty flag optimization** - Only updates when button state changes
