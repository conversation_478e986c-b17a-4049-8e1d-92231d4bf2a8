# Button with Text Rendering

This folder contains an implementation of UI buttons with integrated text rendering using SDL3 and SDL3_ttf. The implementation combines uniform-based button rendering with proper text centering and positioning.

## Key Features

- **Uniform-based button rendering** - Uses transform matrices passed as uniforms to shaders instead of per-button vertex data
- **Integrated text rendering** - Text creation, positioning, and rendering is handled within button functions
- **Proper text centering** - Uses font metrics for accurate horizontal and vertical text alignment
- **Efficient rendering** - Shared vertex data across all buttons with individual transform matrices
- **Mouse interaction** - Hover and click state handling with visual feedback

## Files

- `ButtonText.h` - Header file with button structure and function declarations
- `ButtonText.c` - Implementation of button rendering and text integration
- `testUIButtonText.c` - Test application demonstrating button with text functionality

## Text Centering Implementation

### Problem Solved
Instead of using hardcoded offsets (`-30.0f`, `-12.0f`) for text positioning, the implementation now uses proper font metrics for accurate centering.

### Font Metrics Used
- `TTF_GetFontAscent()` - Gets the font's ascent (positive value from baseline to top)
- `TTF_GetFontDescent()` - Gets the font's descent (negative value from baseline to bottom)  
- `TTF_GetTextSize()` - Gets the actual text dimensions (width and height)

### Visual Center Calculation
```c
// Calculate button center
float button_center_x = x + width / 2.0f;
float button_center_y = y + height / 2.0f;

// Position text so that its visual center aligns with button center
float text_x = button_center_x - text_width / 2.0f;
float text_y = button_center_y - text_height / 2.0f;

// Adjust X position by half a character width to compensate for text positioning offset
float avg_char_width = (float)text_width / strlen(text);
text_x += avg_char_width / 2.0f;
```

### How It Works
1. **Button center** is calculated as the midpoint of button dimensions
2. **Text position** is initially set to center the text bounding box within the button
3. **Character width compensation** - An important discovery: TTF text positioning includes an extra character's width offset
4. **Final adjustment** - Adding half the average character width compensates for this offset and achieves perfect centering

### Key Discovery: Text Positioning Offset
During development, we discovered that TTF text positioning includes approximately one character's width of extra spacing. This causes text to appear offset to the left when using standard bounding box centering calculations. The solution is to adjust the X position by half the average character width:

```c
float avg_char_width = (float)text_width / strlen(text);
text_x += avg_char_width / 2.0f;  // Compensates for TTF positioning offset
```

## Architecture

### Button Structure
```c
typedef struct Button {
    SDL_FRect rect;           // Button position and size
    SDL_FColor color;         // Normal button color
    SDL_FColor hoverColor;    // Hover state color
    SDL_FColor textColor;     // Text color
    char* text;              // Button text string
    UITextObject* text_object; // Integrated text object
    bool isHovered;          // Hover state
    bool isPressed;          // Press state
    bool isDirty;            // Needs redraw flag
} Button;
```

### Integration with UIText Library
- `Button_Init()` creates both button and text object with proper centering
- `Button_Update()` prepares text data for rendering (called outside render pass)
- `Button_Draw()` renders both button and text within the same render pass
- `Button_UpdatePosition()` maintains text centering when button moves

## Rendering Pipeline

1. **Initialization**
   - `Button_InitRenderResources()` - Sets up GPU resources and shaders
   - `Button_Init()` - Creates button with integrated text object

2. **Per-Frame Updates**
   - `Button_Update()` - Prepares text geometry data (outside render pass)
   - `Button_UploadSharedData()` - Uploads shared quad vertex data (once)

3. **Rendering**
   - `Button_Draw()` - Renders buttons using transform matrices
   - Text is rendered on top within the same render pass

## Coordinate System

- **Button coordinates** - Top-left origin (0,0 at top-left of window)
- **Text positioning** - Automatically converted to match button coordinate system
- **NDC conversion** - Transform matrices convert to Normalized Device Coordinates for GPU

## Usage Example

```c
// Initialize button rendering resources
Button_InitRenderResources(device, window);

// Create button with text
Button button;
Button_Init(&button, 100.0f, 100.0f, 150.0f, 50.0f, 
           "Click Me!", text_context, font);

// In render loop
Button_Update(&button, 1, text_context, cmd_buf);  // Outside render pass
Button_Draw(render_pass, cmd_buf, NULL, &button, 1, 
           window_width, window_height, text_context);  // Inside render pass
```

## Dependencies

- SDL3 - Core graphics and windowing
- SDL3_ttf - TrueType font rendering
- UIText library - Text rendering integration (`learn/TTF/uiText/`)

## Shader Requirements

- Vertex shader with transform matrix uniform
- Fragment shader with color uniform
- Compatible with SPIRV, DXIL, and MSL shader formats

## Performance Considerations

- **Shared vertex data** - Single quad geometry shared across all buttons
- **Transform matrices** - Individual positioning via uniforms instead of vertex data
- **Batch text rendering** - All button text rendered together for efficiency
- **Dirty flag optimization** - Only updates when button state changes
